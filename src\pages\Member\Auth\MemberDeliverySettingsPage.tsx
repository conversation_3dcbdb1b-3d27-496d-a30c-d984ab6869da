import React, { useState, useEffect, useCallback } from "react";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { useSDK } from "../../../hooks/useSDK";
import { useToast } from "../../../hooks/useToast";

const MemberDeliverySettingsPage = () => {
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();

  // State management
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deliveryActive, setDeliveryActive] = useState(true);
  const [selectedDays, setSelectedDays] = useState<string[]>([]);
  const [availability, setAvailability] = useState<any[]>([]);
  const [vehicleType, setVehicleType] = useState("Van");
  const [makeModel, setMakeModel] = useState("");
  const [licensePlate, setLicensePlate] = useState("");
  const [insuranceProvider, setInsuranceProvider] = useState("");
  const [insuranceDocument, setInsuranceDocument] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [governmentId, setGovernmentId] = useState("");
  const [driversLicense, setDriversLicense] = useState("");
  const [applicationStatus, setApplicationStatus] = useState("");
  const [lastReviewedBy, setLastReviewedBy] = useState("");
  const [lastVerifiedDate, setLastVerifiedDate] = useState("");

  // Form state for adding new availability
  const [selectedDay, setSelectedDay] = useState("");
  const [startTime, setStartTime] = useState("");
  const [endTime, setEndTime] = useState("");

  const weekDays = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];
  const timeOptions = [
    "09:00",
    "10:00",
    "11:00",
    "12:00",
    "13:00",
    "14:00",
    "15:00",
    "16:00",
    "17:00",
    "18:00",
  ];

  const fetchDeliverySettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/delivery-settings",
        method: "GET",
      });

      console.log("Delivery Settings API Response:", response);

      if (!response.error && response.data) {
        const {
          settings,
          availability,
          vehicle,
          insurance,
          application,
          documents,
        } = response.data;

        // Set delivery status
        if (settings) {
          setDeliveryActive(Boolean(settings.is_active));
        }

        // Set availability
        if (Array.isArray(availability) && availability.length > 0) {
          setAvailability(availability);
          const formattedDays = availability.map(
            (slot: any) =>
              `${String(slot.day_of_week || "")} ${String(slot.start_time || "").slice(0, 5)}-${String(slot.end_time || "").slice(0, 5)}`
          );
          setSelectedDays(formattedDays);
        }

        // Set vehicle information
        if (vehicle) {
          setVehicleType(String(vehicle.vehicle_type || "Van"));
          setMakeModel(String(vehicle.make_model || ""));
          setLicensePlate(String(vehicle.license_plate || ""));
        }

        // Set insurance information
        if (insurance) {
          setInsuranceProvider(String(insurance.provider || ""));
          setInsuranceDocument(String(insurance.document_url || ""));
          setExpiryDate(String(insurance.expiry_date || ""));
        }

        // Set application status
        if (application) {
          setApplicationStatus(String(application.status || ""));
          setLastReviewedBy("Admin Support Team");
          setLastVerifiedDate(
            application.updated_at
              ? new Date(application.updated_at).toLocaleDateString()
              : ""
          );
        }

        // Set documents
        if (Array.isArray(documents)) {
          const govIdDoc = documents.find(
            (doc: any) => doc.document_type === "government_id"
          );
          const licenseDoc = documents.find(
            (doc: any) => doc.document_type === "drivers_license"
          );

          if (govIdDoc) setGovernmentId(String(govIdDoc.document_url || ""));
          if (licenseDoc)
            setDriversLicense(String(licenseDoc.document_url || ""));
        }
      } else {
        setError(
          String(response.message || "Failed to load delivery settings")
        );
      }
    } catch (error: any) {
      console.error("Error fetching delivery settings:", error);
      setError(String(error?.message || "Failed to load delivery settings"));
    } finally {
      setLoading(false);
    }
  }, []);

  const handleDayToggle = (day: string) => {
    setSelectedDays((prev) =>
      prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day]
    );
  };

  const removeDayTag = useCallback(
    async (day: string) => {
      try {
        // Find the availability slot to remove
        const dayName = day.split(" ")[0];
        const slot = availability.find((a: any) => a.day_of_week === dayName);

        if (slot) {
          const response = await sdk.request({
            endpoint: `/v2/api/ebadollar/custom/member/delivery-settings/availability/${slot.id}`,
            method: "DELETE",
          });

          console.log("Remove Availability API Response:", response);

          if (!response.error) {
            setSelectedDays((prev) => prev.filter((d) => d !== day));
            fetchDeliverySettings(); // Refresh data
            success("Availability slot removed successfully");
          } else {
            showError(
              String(response.message || "Failed to remove availability slot")
            );
          }
        }
      } catch (error: any) {
        console.error("Error removing availability:", error);
        showError(
          String(error?.message || "Failed to remove availability slot")
        );
      }
    },
    [availability, sdk, fetchDeliverySettings, success, showError]
  );

  const handleAddTimeSlot = useCallback(async () => {
    if (!selectedDay || !startTime || !endTime) {
      showError("Please select day, start time, and end time");
      return;
    }

    if (startTime >= endTime) {
      showError("Start time must be before end time");
      return;
    }

    try {
      const response = await sdk.request({
        endpoint:
          "/v2/api/ebadollar/custom/member/delivery-settings/availability",
        method: "POST",
        body: {
          dayOfWeek: selectedDay,
          startTime: startTime + ":00",
          endTime: endTime + ":00",
        },
      });

      console.log("Add Time Slot API Response:", response);

      if (!response.error) {
        // Reset form
        setSelectedDay("");
        setStartTime("");
        setEndTime("");

        // Refresh data
        fetchDeliverySettings();
        success("Availability slot added successfully");
      } else {
        showError(
          String(response.message || "Failed to add availability slot")
        );
      }
    } catch (error: any) {
      console.error("Error adding availability:", error);
      showError(String(error?.message || "Failed to add availability slot"));
    }
  }, [
    selectedDay,
    startTime,
    endTime,
    sdk,
    fetchDeliverySettings,
    success,
    showError,
  ]);

  const handleSaveChanges = useCallback(async () => {
    try {
      setLoading(true);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/delivery-settings/save",
        method: "POST",
        body: {
          isActive: deliveryActive,
          vehicleType,
          makeModel,
          licensePlate,
          provider: insuranceProvider,
          documentUrl: insuranceDocument,
          expiryDate,
        },
      });

      console.log("Save Changes API Response:", response);

      if (!response.error) {
        success("Delivery settings saved successfully");
      } else {
        showError(
          String(response.message || "Failed to save delivery settings")
        );
      }
    } catch (error: any) {
      console.error("Error saving delivery settings:", error);
      showError(String(error?.message || "Failed to save delivery settings"));
    } finally {
      setLoading(false);
    }
  }, [
    deliveryActive,
    vehicleType,
    makeModel,
    licensePlate,
    insuranceProvider,
    insuranceDocument,
    expiryDate,
    sdk,
    success,
    showError,
  ]);

  const handleDeliveryStatusChange = useCallback(async (checked: boolean) => {
    try {
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/delivery-settings/status",
        method: "PUT",
        body: { isActive: checked },
      });

      console.log("Delivery Status Change API Response:", response);

      if (!response.error) {
        setDeliveryActive(checked);
        success(
          `Delivery status ${checked ? "activated" : "deactivated"} successfully`
        );
      } else {
        showError(
          String(response.message || "Failed to update delivery status")
        );
      }
    } catch (error: any) {
      console.error("Error updating delivery status:", error);
      showError(String(error?.message || "Failed to update delivery status"));
    }
  }, []);

  // Load delivery settings on component mount
  useEffect(() => {
    fetchDeliverySettings();
  }, []);

  // Show error state if there's an error
  if (error && !loading) {
    return (
      <MemberWrapper>
        <div className="h-full bg-[#0F2C59] overflow-auto">
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <div className="text-4xl mb-4">⚠️</div>
              <p className="text-white mb-4">{error}</p>
              <button
                onClick={fetchDeliverySettings}
                className="px-4 py-2 bg-[#E63946] text-white rounded hover:bg-opacity-90"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  return (
    <MemberWrapper>
      <div className="h-full bg-[#0F2C59] overflow-auto">
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-white mb-2">
              Delivery Settings
            </h1>
            {loading && <div className="text-white text-sm">Loading...</div>}
          </div>

          <div className="space-y-6">
            {/* Delivery Status Section */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Delivery Status
              </h2>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-sm text-gray-700 mr-2">
                    Active for Deliveries
                  </span>
                  <span className="text-gray-400 text-sm">ⓘ</span>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={deliveryActive}
                    onChange={(e) =>
                      handleDeliveryStatusChange(e.target.checked)
                    }
                    className="sr-only peer"
                    disabled={loading}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0F2C59]"></div>
                </label>
              </div>
            </div>

            {/* Weekly Availability Section */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Weekly Availability
              </h2>
              <div className="mb-4">
                <span className="text-sm text-gray-700 mb-3 block">
                  Availability
                </span>
                <div className="flex flex-wrap gap-2 mb-4">
                  {selectedDays.map((day) => (
                    <span
                      key={day}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#0F2C59] text-white"
                    >
                      {day}
                      <button
                        onClick={() => removeDayTag(day)}
                        className="ml-2 text-white hover:text-gray-300"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <select
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                    value={selectedDay}
                    onChange={(e) => setSelectedDay(e.target.value)}
                  >
                    <option value="" disabled>
                      Select day
                    </option>
                    {weekDays.map((day) => (
                      <option key={day} value={day}>
                        {day}
                      </option>
                    ))}
                  </select>

                  <select
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                  >
                    <option value="" disabled>
                      Start time
                    </option>
                    {timeOptions.map((time) => (
                      <option key={time} value={time}>
                        {time}
                      </option>
                    ))}
                  </select>

                  <select
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                  >
                    <option value="" disabled>
                      End time
                    </option>
                    {timeOptions.map((time) => (
                      <option key={time} value={time}>
                        {time}
                      </option>
                    ))}
                  </select>
                </div>

                <button
                  onClick={handleAddTimeSlot}
                  disabled={loading}
                  className="px-4 py-2 bg-[#0F2C59] text-white rounded-md text-sm font-medium hover:bg-[#1a3a6b] disabled:opacity-50"
                >
                  Add Time Slot
                </button>
              </div>
            </div>

            {/* Vehicle Information Section */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Vehicle Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Vehicle Type
                  </label>
                  <select
                    value={vehicleType}
                    onChange={(e) => setVehicleType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                  >
                    <option value="Van">Van</option>
                    <option value="Car">Car</option>
                    <option value="Truck">Truck</option>
                    <option value="Motorcycle">Motorcycle</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Make & Model
                  </label>
                  <input
                    type="text"
                    value={makeModel}
                    onChange={(e) => setMakeModel(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    disabled={loading}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    License Plate
                  </label>
                  <input
                    type="text"
                    value={licensePlate}
                    onChange={(e) => setLicensePlate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    disabled={loading}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Insurance Provider
                  </label>
                  <input
                    type="text"
                    value={insuranceProvider}
                    onChange={(e) => setInsuranceProvider(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    disabled={loading}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Insurance Document
                  </label>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50">
                      <span className="text-gray-600 mr-2">📄</span>
                      <span className="text-gray-700">{insuranceDocument}</span>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600">
                      👁
                    </button>
                    <button className="text-gray-400 hover:text-gray-600">
                      📥
                    </button>
                    <button className="text-gray-400 hover:text-gray-600">
                      🔗
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Expiry Date
                  </label>
                  <input
                    type="date"
                    value={expiryDate}
                    onChange={(e) => setExpiryDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    disabled={loading}
                  />
                </div>
              </div>
            </div>

            {/* Identity Verification Section */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Identity Verification
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Government ID
                  </label>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50">
                      <span className="text-gray-600 mr-2">🆔</span>
                      <span className="text-gray-700">{governmentId}</span>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600">
                      👁
                    </button>
                    <button className="text-gray-400 hover:text-gray-600">
                      🔗
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Driver's License
                  </label>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50">
                      <span className="text-gray-600 mr-2">🪪</span>
                      <span className="text-gray-700">{driversLicense}</span>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600">
                      👁
                    </button>
                    <button className="text-gray-400 hover:text-gray-600">
                      🔗
                    </button>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <span className="text-sm text-gray-700">Status</span>
                <div className="flex items-center mt-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  <span className="text-sm text-green-600 font-medium">
                    Verified
                  </span>
                </div>
              </div>
            </div>

            {/* Admin Verification Info Section */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Admin Verification Info
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Application Status
                  </label>
                  <div className="text-sm text-gray-900 font-medium">
                    {applicationStatus || "Pending"}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Reviewed By
                  </label>
                  <div className="text-sm text-gray-900">
                    {lastReviewedBy || "Not reviewed yet"}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Verified Date
                  </label>
                  <div className="text-sm text-gray-900">
                    {lastVerifiedDate || "Not verified yet"}
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-4">
              <button className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50">
                Request Account Deactivation
              </button>

              <div className="flex space-x-3">
                <button className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50">
                  Cancel
                </button>
                <button
                  onClick={handleSaveChanges}
                  disabled={loading}
                  className="px-6 py-2 bg-[#E63946] text-white rounded-md text-sm font-medium hover:bg-[#d63384] disabled:opacity-50"
                >
                  {loading ? "Saving..." : "Save Changes"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MemberWrapper>
  );
};

export default MemberDeliverySettingsPage;
