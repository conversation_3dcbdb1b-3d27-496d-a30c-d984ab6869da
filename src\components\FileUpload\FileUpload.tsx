import React, { useState, useRef, useCallback } from "react";
import { useSDK } from "../../hooks/useSDK";
import { useToast } from "../../hooks/useToast";
import InteractiveButton from "../InteractiveButton/InteractiveButton";
import CloudUploadIcon from "../../assets/svgs/CloudUploadIcon";

interface FileUploadProps {
  accept?: string;
  maxSize?: number; // in MB
  onUploadSuccess?: (url: string, file: File) => void;
  onUploadError?: (error: string) => void;
  className?: string;
  children?: React.ReactNode;
  disabled?: boolean;
  showPreview?: boolean;
  multiple?: boolean;
}

interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  accept = "image/*",
  maxSize = 5, // 5MB default
  onUploadSuccess,
  onUploadError,
  className = "",
  children,
  disabled = false,
  showPreview = false,
  multiple = false,
}) => {
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const validateFile = useCallback((file: File): FileValidationResult => {
    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      return {
        isValid: false,
        error: `File size must be less than ${maxSize}MB`,
      };
    }

    // Check file type
    if (accept && accept !== "*/*") {
      const acceptedTypes = accept.split(",").map(type => type.trim());
      const fileType = file.type;
      const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`;
      
      const isValidType = acceptedTypes.some(acceptedType => {
        if (acceptedType.startsWith('.')) {
          return acceptedType === fileExtension;
        }
        if (acceptedType.includes('/*')) {
          const baseType = acceptedType.split('/')[0];
          return fileType.startsWith(baseType);
        }
        return acceptedType === fileType;
      });

      if (!isValidType) {
        return {
          isValid: false,
          error: `File type not supported. Accepted types: ${accept}`,
        };
      }
    }

    return { isValid: true };
  }, [accept, maxSize]);

  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0]; // Handle single file for now
    
    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      const errorMessage = validation.error || "Invalid file";
      showError(errorMessage);
      if (onUploadError) {
        onUploadError(errorMessage);
      }
      return;
    }

    // Show preview for images
    if (showPreview && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }

    try {
      setUploading(true);
      setUploadProgress(0);

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 100);

      // Upload file using SDK
      const uploadResponse = await sdk.upload(file);
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (uploadResponse.error) {
        const errorMessage = String(uploadResponse.message || "Failed to upload file");
        showError(errorMessage);
        if (onUploadError) {
          onUploadError(errorMessage);
        }
        return;
      }

      const fileUrl = uploadResponse.data?.url || uploadResponse.url;
      
      if (fileUrl) {
        success("File uploaded successfully!");
        if (onUploadSuccess) {
          onUploadSuccess(fileUrl, file);
        }
      } else {
        const errorMessage = "No file URL returned from upload";
        showError(errorMessage);
        if (onUploadError) {
          onUploadError(errorMessage);
        }
      }
    } catch (error: any) {
      console.error("Error uploading file:", error);
      const errorMessage = String(error?.message || "Failed to upload file");
      showError(errorMessage);
      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setUploading(false);
      setUploadProgress(0);
      
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, [sdk, success, showError, onUploadSuccess, onUploadError, validateFile, showPreview]);

  const handleClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!disabled) {
      const files = e.dataTransfer.files;
      handleFileSelect(files);
    }
  }, [disabled, handleFileSelect]);

  return (
    <div className={`relative ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        className="hidden"
        onChange={(e) => handleFileSelect(e.target.files)}
        disabled={disabled || uploading}
      />
      
      {children ? (
        <div onClick={handleClick} className="cursor-pointer">
          {children}
        </div>
      ) : (
        <div
          onClick={handleClick}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          className={`
            border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer
            hover:border-blue-400 hover:bg-blue-50 transition-colors
            ${disabled || uploading ? 'opacity-50 cursor-not-allowed' : ''}
            ${uploading ? 'border-blue-400 bg-blue-50' : ''}
          `}
        >
          {uploading ? (
            <div className="space-y-2">
              <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="text-sm text-gray-600">Uploading... {uploadProgress}%</p>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <CloudUploadIcon className="w-8 h-8 text-gray-400 mx-auto" />
              <p className="text-sm font-medium text-gray-700">
                Click to upload or drag and drop
              </p>
              <p className="text-xs text-gray-500">
                {accept === "image/*" ? "Images" : accept === "audio/*" ? "Audio files" : "Files"} up to {maxSize}MB
              </p>
            </div>
          )}
        </div>
      )}

      {/* Preview */}
      {showPreview && previewUrl && (
        <div className="mt-4">
          <img 
            src={previewUrl} 
            alt="Preview" 
            className="max-w-full h-32 object-cover rounded-lg border"
          />
        </div>
      )}
    </div>
  );
};

export default FileUpload;
