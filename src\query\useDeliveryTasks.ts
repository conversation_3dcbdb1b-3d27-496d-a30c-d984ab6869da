import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";
import { useToast } from "../hooks/useToast";
import { ToastStatusEnum } from "../utils/Enums";

// Interface definitions
interface IDeliveryTask {
  id: number;
  type: string;
  description?: string;
  price: string;
  currency: string;
  postedTime: string;
  pickupLocation: string;
  pickupAddress: string;
  dropoffLocation: string;
  dropoffAddress: string;
  estimatedDistance: string;
  deliveryDeadline: string;
  specialInstructions?: string;
  requiresFragileHandling: boolean;
  requiresSignature: boolean;
  priority: string;
  senderName: string;
  recipientName: string;
  createdAt: string;
}

interface IDeliveryAgentStats {
  totalDeliveries: number;
  completedDeliveries: number;
  cancelledDeliveries: number;
  successRate: string;
  averageRating: string;
  totalRatings: number;
  totalEarnings: string;
  thisMonthEarnings: string;
  lastDeliveryDate: string | null;
}

interface IDeliveryComplaint {
  id: number;
  complaintType: string;
  category: string;
  subject: string;
  description: string;
  status: string;
  priority: string;
  customerName?: string;
  customerEmail?: string;
  sellerName?: string;
  sellerEmail?: string;
  packageType: string;
  deliveryFee: number;
  route: string;
  adminResponse?: string;
  resolutionNotes?: string;
  ratingImpact?: number;
  compensationAmount?: number;
  dateFiled: string;
  resolvedAt?: string;
}

interface IPackageType {
  value: string;
  label: string;
  count: number;
}

interface IDeliveryTaskFilters {
  page?: number;
  limit?: number;
  search?: string;
  package_type?: string;
  min_price?: number;
  max_price?: number;
  max_distance?: number;
  city?: string;
  sort_by?: string;
  sort_order?: string;
}

interface IApiResponse<T> {
  data?: T;
  [key: string]: any;
}

// Hook to fetch available delivery tasks
export const useAvailableDeliveryTasksQuery = (
  filters: IDeliveryTaskFilters = {}
) => {
  const { sdk } = useSDK();

  return useQuery<IApiResponse<IDeliveryTask[]>>({
    queryKey: ["available-delivery-tasks", filters],
    queryFn: async () => {
      console.log(
        "🚀 Fetching available delivery tasks with filters:",
        filters
      );

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/delivery-tasks/available",
          method: "GET",
          params: filters as Record<string, string | number>,
        });
        console.log("📡 Available Delivery Tasks API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Available Delivery Tasks API Call Error:", error);
        throw error;
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook to accept a delivery task
export const useAcceptDeliveryTaskMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (taskId: number) => {
      console.log("🚀 Accepting delivery task with ID:", taskId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/delivery-tasks/${taskId}/accept`,
          method: "POST",
        });
        console.log("📡 Accept Delivery Task API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Accept Delivery Task API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ Accept Delivery Task Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Delivery task accepted successfully!",
          5000,
          ToastStatusEnum.SUCCESS
        );
        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: ["available-delivery-tasks"],
        });
        queryClient.invalidateQueries({ queryKey: ["delivery-agent-stats"] });
        queryClient.invalidateQueries({ queryKey: ["my-deliveries"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to accept delivery task",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Accept Delivery Task Mutation Error:", err);
      showToast(
        err.message || "An error occurred while accepting the delivery task",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

// Hook to fetch delivery agent statistics
export const useDeliveryAgentStatsQuery = () => {
  const { sdk } = useSDK();

  return useQuery<IApiResponse<IDeliveryAgentStats>>({
    queryKey: ["delivery-agent-stats"],
    queryFn: async () => {
      console.log("🚀 Fetching delivery agent stats");

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/delivery-agent/stats",
          method: "GET",
        });
        console.log("📡 Delivery Agent Stats API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Delivery Agent Stats API Call Error:", error);
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to fetch package types for filtering
export const usePackageTypesQuery = () => {
  const { sdk } = useSDK();

  return useQuery<IApiResponse<IPackageType[]>>({
    queryKey: ["package-types"],
    queryFn: async () => {
      console.log("🚀 Fetching package types");

      try {
        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/delivery-tasks/package-types",
          method: "GET",
        });
        console.log("📡 Package Types API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Package Types API Call Error:", error);
        throw error;
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Hook to fetch complaints as seller (complaints against the delivery agent)
export const useComplaintsAsSellerQuery = (
  filters: { page?: number; limit?: number; status?: string } = {}
) => {
  const { sdk } = useSDK();

  return useQuery<IApiResponse<IDeliveryComplaint[]>>({
    queryKey: ["complaints-as-seller", filters],
    queryFn: async () => {
      console.log("🚀 Fetching complaints as seller with filters:", filters);

      try {
        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/delivery-complaints/as-seller",
          method: "GET",
          params: filters,
        });
        console.log("📡 Complaints as Seller API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Complaints as Seller API Call Error:", error);
        throw error;
      }
    },
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to fetch complaints as buyer (complaints filed by the delivery agent)
export const useComplaintsAsBuyerQuery = (
  filters: { page?: number; limit?: number; status?: string } = {}
) => {
  const { sdk } = useSDK();

  return useQuery<IApiResponse<IDeliveryComplaint[]>>({
    queryKey: ["complaints-as-buyer", filters],
    queryFn: async () => {
      console.log("🚀 Fetching complaints as buyer with filters:", filters);

      try {
        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/delivery-complaints/as-buyer",
          method: "GET",
          params: filters,
        });
        console.log("📡 Complaints as Buyer API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Complaints as Buyer API Call Error:", error);
        throw error;
      }
    },
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to file a new delivery complaint
export const useFileComplaintMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (complaintData: {
      deliveryTaskId: number;
      respondentId: number;
      complaintType: string;
      subject: string;
      description: string;
      evidenceUrls?: string[];
      priority?: string;
    }) => {
      console.log("🚀 Filing delivery complaint with data:", complaintData);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/delivery-complaints",
          method: "POST",
          body: complaintData,
        });
        console.log("📡 File Complaint API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ File Complaint API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ File Complaint Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Complaint filed successfully",
          5000,
          ToastStatusEnum.SUCCESS
        );
        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ["complaints-as-buyer"] });
        queryClient.invalidateQueries({ queryKey: ["complaints-as-seller"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to file complaint",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ File Complaint Mutation Error:", err);
      showToast(
        err.message || "An error occurred while filing the complaint",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

export type {
  IDeliveryTask,
  IDeliveryAgentStats,
  IDeliveryComplaint,
  IPackageType,
  IDeliveryTaskFilters,
};
