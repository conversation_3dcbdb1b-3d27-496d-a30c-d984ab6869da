import React, { useState } from "react";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { useNavigate } from "react-router-dom";
import { EyeIcon } from "@/assets/svgs/EyeIcon";
import TrashIcon from "@/assets/svgs/TrashIcon";
import DownloadIcon from "@/assets/svgs/DownloadIcon";
import SearchIcon from "@/assets/svgs/SearchIcon";
import { Skeleton } from "../../../components/Skeleton";
import { PaginationBar } from "../../../components/PaginationBar";
import {
  useTransactionsQuery,
  useTransactionSummaryQuery,
  useExportTransactionsMutation,
  formatTransactionStatus,
  formatTransactionType,
  ITransactionFilters,
} from "../../../query/useTransactions";

// Remove the old interface and mock data as we'll use the ones from the query hooks

interface IFilters {
  search: string;
  dateRange: string;
  transactionType: string;
  status: string;
}

const FilterComponent = ({
  filters,
  setFilters,
  applyFilters,
  activeTab,
  setActiveTab,
}: {
  filters: IFilters;
  setFilters: React.Dispatch<React.SetStateAction<IFilters>>;
  applyFilters: () => void;
  activeTab: string;
  setActiveTab: React.Dispatch<React.SetStateAction<string>>;
}) => (
  <div className="bg-white rounded-lg p-4 mb-6">
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div className="relative">
        <label
          htmlFor="search"
          className="block text-sm font-medium text-gray-500 mb-1"
        >
          Search
        </label>
        <SearchIcon className="absolute top-9 left-3 w-5 h-5 text-gray-400" />
        <input
          type="text"
          id="search"
          placeholder="Search by item"
          value={filters.search}
          onChange={(e) => setFilters({ ...filters, search: e.target.value })}
          className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm text-gray-900"
        />
      </div>
      <div>
        <label
          htmlFor="date-range"
          className="block text-sm font-medium text-gray-500 mb-1"
        >
          Date Range
        </label>
        <select
          id="date-range"
          value={filters.dateRange}
          onChange={(e) =>
            setFilters({ ...filters, dateRange: e.target.value })
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-900"
        >
          <option>Last 30 days</option>
          <option>Last 7 days</option>
          <option>Last 90 days</option>
        </select>
      </div>
      <div>
        <label
          htmlFor="transaction-type"
          className="block text-sm font-medium text-gray-500 mb-1"
        >
          Transaction Type
        </label>
        <select
          id="transaction-type"
          value={filters.transactionType}
          onChange={(e) =>
            setFilters({ ...filters, transactionType: e.target.value })
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-900"
        >
          <option>All Transactions</option>
          <option>Sales</option>
          <option>Purchases</option>
        </select>
      </div>
      <div>
        <label
          htmlFor="status"
          className="block text-sm font-medium text-gray-500 mb-1"
        >
          Status
        </label>
        <select
          id="status"
          value={filters.status}
          onChange={(e) => setFilters({ ...filters, status: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-900"
        >
          <option>All Statuses</option>
          <option>Completed</option>
          <option>In Dispute</option>
          <option>Refunded</option>
        </select>
      </div>
    </div>
    <div className="mt-4 flex justify-between items-center">
      <div className="flex items-center space-x-2">
        {["All", "My Sales", "My Purchases"].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              activeTab === tab
                ? "bg-[#0D3166] text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            {tab}
          </button>
        ))}
      </div>
      <button
        onClick={applyFilters}
        className="px-6 py-2 bg-[#0D3166] text-white rounded-md text-sm font-medium"
      >
        Apply Filters
      </button>
    </div>
  </div>
);

const MemberTransactionsListPage = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("All");
  const [filters, setFilters] = useState<IFilters>({
    search: "",
    dateRange: "Last 30 days",
    transactionType: "All Transactions",
    status: "All Statuses",
  });
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  // Query filters
  const queryFilters: ITransactionFilters = {
    page: currentPage,
    limit: itemsPerPage,
    search: filters.search,
    dateRange: filters.dateRange,
    transactionType: filters.transactionType,
    status: filters.status,
    tab:
      activeTab === "All"
        ? "All"
        : activeTab === "My Sales"
          ? "My Sales"
          : "My Purchases",
  };

  // Query hooks
  const {
    data: transactionsData,
    isLoading: transactionsLoading,
    error: transactionsError,
    refetch: refetchTransactions,
  } = useTransactionsQuery(queryFilters);

  const {
    data: summaryData,
    isLoading: summaryLoading,
    error: summaryError,
  } = useTransactionSummaryQuery();

  const { mutate: exportTransactions, isPending: isExporting } =
    useExportTransactionsMutation();

  // Get data from queries
  const transactions =
    transactionsData &&
    typeof transactionsData === "object" &&
    Array.isArray((transactionsData as any).data)
      ? (transactionsData as any).data
      : [];
  const pagination =
    transactionsData &&
    typeof transactionsData === "object" &&
    typeof (transactionsData as any).pagination === "object" &&
    (transactionsData as any).pagination !== null
      ? (transactionsData as any).pagination
      : { page: 1, limit: itemsPerPage, total: 0, totalPages: 1 };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setCurrentPage(1); // Reset to first page when changing tabs
  };

  const handleExport = () => {
    exportTransactions({
      format: "csv",
      filters: queryFilters,
    });
  };

  const getStatusPill = (status: string) => {
    const { color } = formatTransactionStatus(status);
    return `px-3 py-1 text-xs font-medium rounded-full ${color}`;
  };

  const applyFilters = () => {
    setCurrentPage(1); // Reset to first page when applying filters
    refetchTransactions();
  };

  // Transactions are already paginated from the API
  const paginatedTransactions = Array.isArray(transactions) ? transactions : [];
  const totalPages =
    typeof pagination.totalPages === "number" ? pagination.totalPages : 1;

  const summary =
    summaryData &&
    typeof summaryData === "object" &&
    "sales" in summaryData &&
    "purchases" in summaryData &&
    "fees" in summaryData
      ? (summaryData as any)
      : {
          sales: { total: "0.00", count: 0, currency: "eBa$" },
          purchases: { total: "0.00", count: 0, currency: "eBa$" },
          fees: { total: "0.00", average: "0.00", currency: "eBa$" },
        };

  return (
    <MemberWrapper>
      <div className="p-6 bg-[#0D3166] min-h-screen">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold text-white">Transaction History</h1>
          <button
            onClick={handleExport}
            disabled={isExporting}
            className="flex items-center px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
          >
            <DownloadIcon className="w-5 h-5 mr-2" />
            {isExporting ? "Exporting..." : "Export"}
          </button>
        </div>

        <FilterComponent
          filters={filters}
          setFilters={setFilters}
          applyFilters={applyFilters}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
        />

        <div className="bg-white rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  {[
                    "Date",
                    "Listing",
                    "Item",
                    "Service",
                    "Type",
                    "Counterparty",
                    "Amount",
                    "Fee",
                    "Net Received/Paid",
                    "Status",
                    "Actions",
                  ].map((header) => (
                    <th
                      key={header}
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transactionsLoading ? (
                  // Loading skeleton
                  [...Array(itemsPerPage)].map((_, index) => (
                    <tr key={index}>
                      {[...Array(11)].map((_, cellIndex) => (
                        <td key={cellIndex} className="px-4 py-3">
                          <Skeleton className="h-4 w-20" />
                        </td>
                      ))}
                    </tr>
                  ))
                ) : transactionsError ? (
                  <tr>
                    <td colSpan={11} className="px-4 py-8 text-center">
                      <div className="text-red-500 mb-4">
                        Failed to load transactions
                      </div>
                      <button
                        onClick={() => refetchTransactions()}
                        className="bg-[#0D3166] text-white px-4 py-2 rounded-md hover:bg-[#1a4480]"
                      >
                        Retry
                      </button>
                    </td>
                  </tr>
                ) : paginatedTransactions.length === 0 ? (
                  <tr>
                    <td
                      colSpan={11}
                      className="px-4 py-8 text-center text-gray-500"
                    >
                      No transactions found
                    </td>
                  </tr>
                ) : (
                  paginatedTransactions.map((transaction: any) => (
                    <tr key={transaction.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap">
                        {transaction.date}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900 font-medium whitespace-nowrap">
                        {transaction.listing}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900"></td>
                      <td className="px-4 py-3 text-sm text-gray-900"></td>
                      <td className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                        {transaction.type}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap">
                        {transaction.counterparty}
                      </td>
                      <td className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                        {transaction.amount}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                        {transaction.fee}
                      </td>
                      <td className="px-4 py-3 text-sm font-bold text-gray-900 whitespace-nowrap">
                        {transaction.net_received_paid}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        <span className={getStatusPill(transaction.status)}>
                          {transaction.status}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <button
                            className="text-gray-400 hover:text-gray-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(
                                `/member/transactions/view/${transaction.id}`
                              );
                            }}
                          >
                            <EyeIcon className="w-5 h-5" />
                          </button>
                          <button
                            className="text-gray-400 hover:text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Handle delete action
                            }}
                          >
                            <TrashIcon className="w-5 h-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 mb-6">
            <div className="text-sm text-gray-300">
              Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
              {Math.min(pagination.page * pagination.limit, pagination.total)}{" "}
              of {pagination.total} entries
            </div>
            <div className="flex justify-center">
              <PaginationBar
                currentPage={pagination.page}
                pageCount={pagination.totalPages}
                pageSize={pagination.limit}
                canPreviousPage={pagination.page > 1}
                canNextPage={pagination.page < pagination.totalPages}
                updatePageSize={() => {}}
                updateCurrentPage={handlePageChange}
                startSize={6}
                multiplier={6}
                canChangeLimit={false}
              />
            </div>
          </div>
        )}

        {/* Summary Cards */}
        <div className="grid md:grid-cols-3 gap-6 mt-4">
          <div className="bg-white rounded-lg p-6">
            <p className="text-sm text-gray-600 mb-1">Sales Summary</p>
            {summaryLoading ? (
              <div>
                <Skeleton className="h-8 w-24 mb-1" />
                <Skeleton className="h-4 w-32" />
              </div>
            ) : summaryError ? (
              <div>
                <p className="text-2xl font-bold text-red-500 mb-1">Error</p>
                <p className="text-sm text-red-500">Failed to load</p>
              </div>
            ) : summary && summary.sales ? (
              <div>
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  {summary.sales?.currency ?? "eBa$"}{" "}
                  {summary.sales?.total ?? "0.00"}
                </p>
                <p className="text-sm text-gray-500 flex items-center">
                  <span className="text-green-500 mr-1">📈</span>
                  {summary.sales?.count ?? 0} transactions this month
                </p>
              </div>
            ) : (
              <div>
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  eBa$ 0.00
                </p>
                <p className="text-sm text-gray-500">No data available</p>
              </div>
            )}
          </div>

          <div className="bg-white rounded-lg p-6">
            <p className="text-sm text-gray-600 mb-1">Purchases Summary</p>
            {summaryLoading ? (
              <div>
                <Skeleton className="h-8 w-24 mb-1" />
                <Skeleton className="h-4 w-32" />
              </div>
            ) : summaryError ? (
              <div>
                <p className="text-2xl font-bold text-red-500 mb-1">Error</p>
                <p className="text-sm text-red-500">Failed to load</p>
              </div>
            ) : summary && summary.purchases ? (
              <div>
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  {summary.purchases?.currency ?? "eBa$"}{" "}
                  {summary.purchases?.total ?? "0.00"}
                </p>
                <p className="text-sm text-gray-500 flex items-center">
                  <span className="text-blue-500 mr-1">📊</span>
                  {summary.purchases?.count ?? 0} transactions this month
                </p>
              </div>
            ) : (
              <div>
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  eBa$ 0.00
                </p>
                <p className="text-sm text-gray-500">No data available</p>
              </div>
            )}
          </div>

          <div className="bg-white rounded-lg p-6">
            <p className="text-sm text-gray-600 mb-1">Total Fees Paid</p>
            {summaryLoading ? (
              <div>
                <Skeleton className="h-8 w-24 mb-1" />
                <Skeleton className="h-4 w-32" />
              </div>
            ) : summaryError ? (
              <div>
                <p className="text-2xl font-bold text-red-500 mb-1">Error</p>
                <p className="text-sm text-red-500">Failed to load</p>
              </div>
            ) : summary && summary.fees ? (
              <div>
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  {summary.fees?.currency ?? "eBa$"}{" "}
                  {summary.fees?.total ?? "0.00"}
                </p>
                <p className="text-sm text-gray-500 flex items-center">
                  <span className="mr-1">ℹ️</span>
                  Average fee: {summary.fees?.currency ?? "eBa$"}{" "}
                  {summary.fees?.average ?? "0.00"}
                </p>
              </div>
            ) : (
              <div>
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  eBa$ 0.00
                </p>
                <p className="text-sm text-gray-500">No data available</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </MemberWrapper>
  );
};

export default MemberTransactionsListPage;
