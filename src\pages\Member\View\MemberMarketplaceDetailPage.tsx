import React, { useEffect, useState } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { MemberWrapper } from "../../../components/MemberWrapper";
import InteractiveButton from "../../../components/InteractiveButton/InteractiveButton";
import { useSDK } from "../../../hooks/useSDK";
import { MkdLoader } from "../../../components/MkdLoader";
import { StarIcon } from "@/assets/svgs";

interface ISellerInfo {
  name: string;
  rating: number;
  reviews: number;
  member_since: string;
  credit_score: string;
  location: string;
  verified: boolean;
}

interface IBookingOptions {
  service_location: string;
  availability: string;
  time_slots: string;
  payment: string;
}

interface IListing {
  id: number;
  name: string;
  seller: string;
  seller_id?: number;
  price: string;
  status: string;
  description?: string;
  image?: string;
  images?: string[];
  category?: string;
  created_at: string;
  updated_at: string;
  rating?: number;
  sponsored?: boolean;
  type?: string;
  tags?: string[];
  seller_info?: ISellerInfo;
  availableSizes?: { short: string; long: string }[];
  price_per_hour?: boolean;
  booking_options?: IBookingOptions;
}

const MemberMarketplaceDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [listing, setListing] = useState<IListing | null>(null);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showMakeOfferModal, setShowMakeOfferModal] = useState(false);
  const [offerAmount, setOfferAmount] = useState("");
  const [offerMessage, setOfferMessage] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState("L");
  const [similarListings, setSimilarListings] = useState<IListing[]>([]);
  const [loadingSimilar, setLoadingSimilar] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState("ebadollar");
  const [selectedDeliveryMethod, setSelectedDeliveryMethod] =
    useState("eba_delivery");
  const [shippingAddress, setShippingAddress] = useState({
    fullName: "",
    phoneNumber: "",
    country: "",
    addressLine1: "",
    addressLine2: "",
    city: "",
    provinceState: "",
    postalCode: "",
    saveToAccount: false,
  });
  const { sdk } = useSDK();

  useEffect(() => {
    if (id) {
      fetchListing();
    }
  }, [id]);

  const fetchListing = async () => {
    setLoading(true);
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/marketplace/listings/${id}`,
        method: "GET",
      });

      if (!response.error && response.data) {
        setListing(response.data);

        // Track the view
        try {
          await sdk.request({
            endpoint: `/v2/api/ebadollar/custom/member/marketplace/listings/${id}/view`,
            method: "POST",
            body: {
              source: "direct",
              referrer: document.referrer || null,
            },
          });
        } catch (viewError) {
          console.log("View tracking failed (non-critical):", viewError);
        }

        // Fetch similar listings after setting the main listing
        setTimeout(() => {
          fetchSimilarListings();
        }, 100);
      } else {
        console.error("Error fetching listing:", response.message);
        setListing(null);
      }
    } catch (error) {
      console.error("Error fetching listing:", error);
      setListing(null);
    } finally {
      setLoading(false);
    }
  };

  const handleBuyNowClick = () => {
    setShowPaymentModal(true);
  };

  const handlePurchase = async () => {
    if (!listing) return;

    setPurchasing(true);
    try {
      // Prepare the purchase data
      const purchaseData = {
        quantity: quantity,
        selectedSize: selectedSize,
        payment_method: selectedPaymentMethod,
        delivery_method: selectedDeliveryMethod,
        message: `Purchase request for ${listing.name}`,
      };

      // Add shipping address if delivery method requires it
      if (
        selectedDeliveryMethod === "eba_delivery" ||
        selectedDeliveryMethod === "local_delivery"
      ) {
        purchaseData.shipping_address = {
          full_name: shippingAddress.fullName,
          phone_number: shippingAddress.phoneNumber,
          country: shippingAddress.country,
          address_line_1: shippingAddress.addressLine1,
          address_line_2: shippingAddress.addressLine2,
          city: shippingAddress.city,
          province_state: shippingAddress.provinceState,
          postal_code: shippingAddress.postalCode,
          save_to_account: shippingAddress.saveToAccount,
        };
      }

      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/marketplace/listings/${listing.id}/purchase`,
        method: "POST",
        body: purchaseData,
      });

      if (!response.error && response.data) {
        setShowConfirmModal(false);
        setShowPaymentModal(false);
        alert(
          `Purchase initiated successfully! Transaction ID: ${response.data.transaction_group_id}. Check your transactions for updates.`
        );
        navigate("/member/transactions");
      } else {
        console.error("Error creating transaction:", response.message);
        alert(
          response.message || "Failed to initiate purchase. Please try again."
        );
      }
    } catch (error) {
      console.error("Error creating transaction:", error);
      alert("Failed to initiate purchase. Please try again.");
    } finally {
      setPurchasing(false);
    }
  };

  const handleContactSeller = async () => {
    if (!listing) return;

    const message = prompt("Enter your message to the seller:");
    if (!message || message.trim().length === 0) return;

    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/marketplace/listings/${listing.id}/contact`,
        method: "POST",
        body: {
          message: message.trim(),
          inquiry_type: "general",
        },
      });

      if (!response.error && response.data) {
        alert("Message sent successfully to the seller!");
      } else {
        console.error("Error sending message:", response.message);
        alert(response.message || "Failed to send message. Please try again.");
      }
    } catch (error) {
      console.error("Error sending message:", error);
      alert("Failed to send message. Please try again.");
    }
  };

  const handleMakeOffer = () => {
    setShowMakeOfferModal(true);
  };

  const handleSubmitOffer = async () => {
    if (!listing || !offerAmount || parseFloat(offerAmount) <= 0) {
      alert("Please enter a valid offer amount.");
      return;
    }

    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/marketplace/listings/${listing.id}/offer`,
        method: "POST",
        body: {
          offer_amount: parseFloat(offerAmount),
          quantity: quantity,
          message: offerMessage.trim(),
          expires_in_hours: 24,
        },
      });

      if (!response.error && response.data) {
        alert(
          `Offer of eb$${offerAmount} submitted successfully! The seller will be notified.`
        );
        setShowMakeOfferModal(false);
        setOfferAmount("");
        setOfferMessage("");
      } else {
        console.error("Error submitting offer:", response.message);
        alert(response.message || "Failed to submit offer. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting offer:", error);
      alert("Failed to submit offer. Please try again.");
    }
  };

  const handleCancelOffer = () => {
    setShowMakeOfferModal(false);
    setOfferAmount("");
    setOfferMessage("");
  };

  const renderStars = (rating?: number) => {
    if (!rating) return null;
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`w-4 h-4 ${
          index < Math.floor(rating) ? "text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  const fetchSimilarListings = async () => {
    if (!listing) return;

    setLoadingSimilar(true);
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/marketplace/listings/${listing.id}/similar`,
        method: "GET",
        params: { limit: 6 },
      });

      if (!response.error && response.data) {
        setSimilarListings(response.data);
      } else {
        console.error("Error fetching similar listings:", response.message);
        setSimilarListings([]);
      }
    } catch (error) {
      console.error("Error fetching similar listings:", error);
      setSimilarListings([]);
    } finally {
      setLoadingSimilar(false);
    }
  };

  const getSimilarListings = () => {
    return similarListings.slice(0, 3);
  };

  const getProductImages = () => {
    if (!listing) return [];

    // If listing has multiple images, use them
    if (listing.images && Array.isArray(listing.images)) {
      return listing.images;
    }

    // Otherwise, use the main image
    if (listing.image) {
      return [listing.image];
    }

    // Fallback to a default image
    return [
      "https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?q=80&w=800",
    ];
  };

  if (loading) {
    return (
      <MemberWrapper>
        <div className="flex justify-center items-center py-12">
          <MkdLoader />
        </div>
      </MemberWrapper>
    );
  }

  if (!listing) {
    return (
      <MemberWrapper>
        <div className="p-6 bg-[#001f3f] min-h-screen">
          <div className="text-center py-12">
            <div className="text-white text-lg mb-2">Listing not found</div>
            <InteractiveButton
              onClick={() => navigate("/member/marketplace")}
              className="bg-[#e53e3e] text-white px-4 py-2 rounded-md hover:bg-[#c53030]"
            >
              Back to Marketplace
            </InteractiveButton>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  return (
    <MemberWrapper>
      <div className="bg-[#0D3166] min-h-screen text-white">
        <div className="container mx-auto px-6 py-8">
          {/* Back Button */}
          <button
            onClick={() => navigate(-1)}
            className="flex items-center gap-2 text-sm mb-6 hover:text-gray-300"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Listing Details
          </button>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column */}
            <div className="lg:col-span-2 space-y-8">
              {/* Product Header */}
              <div>
                <h1 className="text-3xl font-bold mb-2">{listing.name}</h1>
                <div className="flex items-center gap-4 text-sm text-gray-400">
                  <span>Listed 3 days ago</span>
                  <span className="flex items-center gap-2 bg-gray-700 px-2 py-1 rounded">
                    <span>Type:</span>
                    <span className="font-semibold">{listing.type}</span>
                  </span>
                </div>
              </div>

              {/* Image Gallery */}
              <div>
                <div className="relative mb-4">
                  <img
                    src={getProductImages()[selectedImageIndex]}
                    alt={listing.name}
                    className="w-full h-auto object-cover rounded-lg cursor-zoom-in"
                  />
                  <div className="absolute top-4 left-4">
                    {listing.sponsored && (
                      <span className="bg-yellow-400 text-black px-3 py-1 rounded text-xs font-bold flex items-center gap-1">
                        <StarIcon className="w-3 h-3" />
                        Sponsored
                      </span>
                    )}
                  </div>
                  <button
                    onClick={() =>
                      setSelectedImageIndex(Math.max(0, selectedImageIndex - 1))
                    }
                    className="absolute top-1/2 left-4 transform -translate-y-1/2 bg-white bg-opacity-50 hover:bg-opacity-75 rounded-full p-2"
                  >
                    <svg
                      className="w-6 h-6 text-gray-800"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 19l-7-7 7-7"
                      />
                    </svg>
                  </button>
                  <button
                    onClick={() =>
                      setSelectedImageIndex(
                        Math.min(
                          getProductImages().length - 1,
                          selectedImageIndex + 1
                        )
                      )
                    }
                    className="absolute top-1/2 right-4 transform -translate-y-1/2 bg-white bg-opacity-50 hover:bg-opacity-75 rounded-full p-2"
                  >
                    <svg
                      className="w-6 h-6 text-gray-800"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full">
                    {selectedImageIndex + 1} / {getProductImages().length}
                  </div>
                  <div className="absolute bottom-4 right-4 text-sm flex items-center gap-1">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                    Click to zoom
                  </div>
                </div>
                <div className="flex gap-2">
                  {getProductImages().map((img, index) => (
                    <img
                      key={index}
                      src={img}
                      alt={`thumbnail ${index}`}
                      className={`w-20 h-20 object-cover rounded-md cursor-pointer border-2 ${
                        selectedImageIndex === index
                          ? "border-[#F52D2A]"
                          : "border-transparent"
                      }`}
                      onClick={() => setSelectedImageIndex(index)}
                    />
                  ))}
                </div>
              </div>

              {/* Description */}
              <div className="bg-white text-black p-6 rounded-lg">
                <h2 className="text-xl font-bold mb-4">Description</h2>
                <div
                  className="text-sm space-y-4 whitespace-pre-wrap"
                  dangerouslySetInnerHTML={{
                    __html: listing.description
                      ? listing.description.replace(
                          /Key specifications:|This laptop is in excellent condition|This is perfect for gamers/g,
                          (match) => `<strong>${match}</strong>`
                        )
                      : "",
                  }}
                />
                <div className="mt-6 pt-4 border-t border-gray-200 text-xs text-gray-500 flex justify-between items-center">
                  <div className="flex gap-4">
                    <span>
                      Listed on{" "}
                      {new Date(listing.created_at).toLocaleDateString()}
                    </span>
                    <span>
                      Expires on{" "}
                      {new Date(
                        new Date(listing.created_at).setMonth(
                          new Date(listing.created_at).getMonth() + 1
                        )
                      ).toLocaleDateString()}
                    </span>
                    <span>{listing.category?.split(">")[0].trim()}</span>
                  </div>
                  <button className="flex items-center gap-1 text-red-500 hover:underline">
                    <svg
                      className="w-3 h-3"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M10 2a8 8 0 100 16 8 8 0 000-16zM9 12a1 1 0 112 0 1 1 0 01-2 0zm1-8a1 1 0 011 1v4a1 1 0 11-2 0V5a1 1 0 011-1z" />
                    </svg>
                    Report Listing
                  </button>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-200 flex items-center gap-4">
                  <span className="text-sm font-semibold">
                    Share this listing:
                  </span>
                  <div className="flex gap-3 text-xl">
                    <a href="#" className="hover:text-blue-600">
                      f
                    </a>
                    <a href="#" className="hover:text-blue-400">
                      in
                    </a>
                    <a href="#" className="hover:text-gray-600">
                      ✉
                    </a>
                  </div>
                </div>
              </div>

              {/* Delivery & Shipping or Booking Options */}
              {listing.type === "Service" && listing.booking_options ? (
                <div className="bg-white text-black p-6 rounded-lg">
                  <h2 className="text-xl font-bold mb-4">Booking Options</h2>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Service Location</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.service_location}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Availability</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.availability}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Time Slots</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.time_slots}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Payment</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.payment}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white text-black p-6 rounded-lg">
                  <h2 className="text-xl font-bold mb-4">
                    Delivery & Shipping
                  </h2>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">
                          Option 1: eBa Delivery
                        </h4>
                        <p className="text-xs text-gray-500">
                          Trackable delivery managed by the platform.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">
                          Option 2: Other Local Delivery
                        </h4>
                        <p className="text-xs text-gray-500">
                          Seller delivers within the same city/region.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-red-500 mt-1">✗</span>
                      <div>
                        <h4 className="font-semibold">
                          Option 3: International Delivery
                        </h4>
                        <p className="text-xs text-gray-500">
                          Seller ships via FedEx, DHL, etc. to other countries.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">Option 4: Pickup</h4>
                        <p className="text-xs text-gray-500">
                          Buyer collects the item from seller's location.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">Option 5: Meet-up</h4>
                        <p className="text-xs text-gray-500">
                          Buyer and seller meet in a public place to exchange
                          the item.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Similar Listings */}
              <div>
                <h2 className="text-xl font-bold mb-4">Similar Listings</h2>
                {loadingSimilar ? (
                  <div className="flex justify-center py-8">
                    <MkdLoader />
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {getSimilarListings().map((item) => (
                      <div
                        key={item.id}
                        className="bg-white rounded-lg text-black overflow-hidden relative"
                      >
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-40 object-cover"
                        />
                        <div className="p-4">
                          <div className="flex gap-2 mb-2">
                            {item.sponsored && (
                              <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                ⭐ Sponsored
                              </span>
                            )}
                            {item.id === 2 && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                New
                              </span>
                            )}
                          </div>
                          <h4 className="font-semibold text-sm mb-2">
                            {item.name}
                          </h4>
                          <p className="text-[#F52D2A] font-bold text-lg mb-2">
                            eBa$ {item.price}
                          </p>
                          <p className="text-xs text-gray-500 mb-3 truncate">
                            {item.description}
                          </p>
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <div className="flex items-center gap-1">
                              {renderStars(item.rating)}
                            </div>
                            <div className="flex items-center gap-1">
                              <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
                              <span>{item.seller}</span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between text-xs text-gray-500 mt-2">
                            <span>{item.seller_info?.location}</span>
                            <span>Added {item.created_at}</span>
                          </div>
                        </div>
                        <button className="absolute top-2 right-2 bg-white rounded-full p-1.5 shadow">
                          <svg
                            className="w-4 h-4 text-gray-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                            />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Right Column (Sticky) */}
            <div className="lg:sticky top-8 self-start space-y-6">
              {/* Purchase Card */}
              <div className="bg-white text-black p-6 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <p className="text-3xl font-bold text-[#0D3166]">
                    eBa$ {listing.price}
                    {listing.price_per_hour && "/hr"}
                  </p>
                  <button>
                    <svg
                      className="w-6 h-6 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                      />
                    </svg>
                  </button>
                </div>

                <p className="text-sm text-gray-500 mb-3 flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M17.707 3.293a1 1 0 00-1.414 0L15.586 4H12a1 1 0 00-1 1v.586l-2.707-2.707a1 1 0 00-1.414 0L5.293 4.464a1 1 0 000 1.414l7 7a1 1 0 001.414 0l4-4a1 1 0 000-1.414l-1.586-1.586zM11 5H6.414l1.293-1.293L11 6.414V5zM9 9a1 1 0 011-1h1a1 1 0 110 2H9a1 1 0 01-1-1zm-5 5.586l-1.293-1.293a1 1 0 010-1.414l1.586-1.586a1 1 0 011.414 0L8.414 11H13a1 1 0 011 1v.586l2.707 2.707a1 1 0 010 1.414l-1.586 1.586a1 1 0 01-1.414 0L12 16.586V18a1 1 0 01-1 1h-.586l-2.707 2.707a1 1 0 01-1.414 0L4.707 19.121a1 1 0 010-1.414l1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {listing.category}
                </p>

                <div className="flex gap-2 mb-4">
                  {listing.tags?.map((tag) => (
                    <span
                      key={tag}
                      className={`text-xs px-2 py-1 rounded font-semibold ${
                        tag === "Sponsored"
                          ? "bg-blue-100 text-[#0D3166]"
                          : "bg-gray-100 text-gray-700"
                      }`}
                    >
                      {tag === "Sponsored" && "⭐ "}
                      {tag}
                    </span>
                  ))}
                </div>

                {listing.type === "Service" ? (
                  <div className="space-y-3">
                    <InteractiveButton className="w-full bg-[#0D3166] text-white py-3 rounded-lg font-semibold">
                      Book Service
                    </InteractiveButton>
                    <p className="text-xs text-gray-500 text-center">
                      Payment will be held in escrow until service is confirmed.
                    </p>
                    <InteractiveButton className="w-full border border-gray-300 py-3 rounded-lg font-semibold text-gray-700 hover:bg-gray-50">
                      Make Offer
                    </InteractiveButton>
                  </div>
                ) : (
                  <>
                    {listing.category?.toLowerCase().includes("clothing") &&
                    listing.availableSizes ? (
                      <>
                        <div className="mb-4">
                          <label className="text-sm font-semibold text-gray-800 mb-2 block">
                            Select Size
                          </label>
                          <div className="flex gap-2 flex-wrap">
                            {listing.availableSizes.map((sizeInfo) => (
                              <button
                                key={sizeInfo.short}
                                onClick={() => setSelectedSize(sizeInfo.short)}
                                className={`flex flex-col items-center justify-center p-2 border rounded-md w-16 h-16 transition-colors ${
                                  selectedSize === sizeInfo.short
                                    ? "border-red-500 bg-red-50"
                                    : "border-gray-300"
                                }`}
                              >
                                <span className="font-bold text-gray-800">
                                  {sizeInfo.short}
                                </span>
                                <span className="text-xs text-gray-500">
                                  {sizeInfo.long}
                                </span>
                              </button>
                            ))}
                          </div>
                        </div>
                        <p className="text-xs text-gray-500 mb-4 flex items-center gap-1">
                          <svg
                            className="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M14.08 7.36a.75.75 0 01.07 1.058l-3.32 4.98a.75.75 0 01-1.12.08l-2.43-2.43a.75.75 0 111.06-1.06l1.87 1.87 2.76-4.14a.75.75 0 011.06-.07zM18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-7a.75.75 0 01.75.75v12.5a.75.75 0 01-1.5 0V3.75A.75.75 0 0110 3z" />
                          </svg>
                          Runs true to size. See chart for details.
                        </p>
                      </>
                    ) : (
                      <div className="flex items-center gap-4 mb-6">
                        <label className="text-sm font-medium">Quantity</label>
                        <div className="flex items-center border border-gray-300 rounded">
                          <button
                            onClick={() =>
                              setQuantity(Math.max(1, quantity - 1))
                            }
                            className="px-3 py-1 text-lg text-gray-500 hover:bg-gray-100"
                          >
                            -
                          </button>
                          <span className="px-4 py-1 text-center w-12">
                            {quantity}
                          </span>
                          <button
                            onClick={() => setQuantity(quantity + 1)}
                            className="px-3 py-1 text-lg text-gray-500 hover:bg-gray-100"
                          >
                            +
                          </button>
                        </div>
                      </div>
                    )}
                    <div className="space-y-3">
                      <InteractiveButton
                        onClick={handleBuyNowClick}
                        className="w-full bg-[#0D3166] text-white py-3 rounded-lg font-semibold"
                      >
                        Buy Now
                      </InteractiveButton>
                      <InteractiveButton className="w-full border border-gray-300 py-3 rounded-lg font-semibold text-gray-700 hover:bg-gray-50">
                        Make Offer
                      </InteractiveButton>
                    </div>
                    {listing.category?.toLowerCase().includes("clothing") && (
                      <button className="text-sm text-gray-700 mt-4 flex items-center justify-center w-full gap-1 hover:underline">
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M14.08 7.36a.75.75 0 01.07 1.058l-3.32 4.98a.75.75 0 01-1.12.08l-2.43-2.43a.75.75 0 111.06-1.06l1.87 1.87 2.76-4.14a.75.75 0 011.06-.07zM18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-7a.75.75 0 01.75.75v12.5a.75.75 0 01-1.5 0V3.75A.75.75 0 0110 3z" />
                        </svg>
                        View Size Chart
                      </button>
                    )}
                  </>
                )}
              </div>

              {/* Seller Information */}
              <div className="bg-white text-black p-6 rounded-lg">
                <h3 className="text-lg font-bold mb-4">Seller Information</h3>
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div>
                    <p className="font-bold">
                      {listing.seller_info?.name || "N/A"}
                    </p>
                    <div className="flex items-center gap-1 text-xs">
                      {renderStars(listing.seller_info?.rating)}
                      <span>
                        {listing.seller_info?.rating} (
                        {listing.seller_info?.reviews} ratings)
                      </span>
                    </div>
                  </div>
                </div>
                <div className="text-xs space-y-2">
                  <div className="flex justify-between">
                    <span>Member since</span>
                    <span className="font-semibold">
                      {listing.seller_info?.member_since}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Credit Score</span>
                    <span className="font-semibold">
                      {listing.seller_info?.credit_score}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Location</span>
                    <span className="font-semibold">
                      {listing.seller_info?.location}
                    </span>
                  </div>
                </div>
                {listing.seller_info?.verified && (
                  <p className="text-xs text-green-600 flex items-center gap-1 mt-4">
                    <svg
                      className="w-3 h-3"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Verified Seller
                  </p>
                )}
                <InteractiveButton className="w-full mt-4 border border-gray-300 py-2 rounded-lg font-semibold text-sm">
                  Contact Seller
                </InteractiveButton>
              </div>

              {/* Safety Tips */}
              <div className="bg-white text-black p-6 rounded-lg">
                <h3 className="text-lg font-bold mb-4">Safety Tips</h3>
                <ul className="text-xs space-y-2 text-gray-600">
                  <li className="flex items-start gap-2">
                    <span className="text-green-500 mt-1">✓</span> Always use
                    eBa's secure payment system
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-500 mt-1">✓</span> Never share
                    personal financial information
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-500 mt-1">✓</span> Review seller
                    ratings before purchasing
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-500 mt-1">✓</span> Report
                    suspicious activity immediately
                  </li>
                </ul>
                <a
                  href="#"
                  className="text-xs text-blue-600 hover:underline mt-4 block text-center"
                >
                  Learn more about safe trading
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Method Selection Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-md mx-4 relative">
            {/* Close Button */}
            <button
              onClick={() => setShowPaymentModal(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-xl font-bold"
            >
              ×
            </button>

            {/* Modal Header */}
            <div className="p-6 pb-4">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Select Payment Method
              </h2>
              <p className="text-sm text-gray-600 mb-6">
                Choose how you'd like to pay
              </p>
              <p className="text-sm text-gray-600 mb-6">
                Select a payment method to complete your purchase of Premium
                Gaming Laptop.
              </p>
            </div>

            {/* Payment Options */}
            <div className="px-6 pb-4 space-y-3">
              {/* eBaDollar Account Option */}
              <div className="border border-gray-200 rounded-lg p-4">
                <label className="flex items-start gap-3 cursor-pointer">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="ebadollar"
                    checked={selectedPaymentMethod === "ebadollar"}
                    onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                    className="mt-1 w-4 h-4 text-[#E63946] border-gray-300 focus:ring-[#E63946]"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="w-3 h-3 bg-[#E63946] rounded-full"></span>
                      <span className="font-medium text-gray-900">
                        Pay from your eBaDollar account
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">
                      Available balance: EBA$ 2,500.00
                    </p>
                  </div>
                </label>
              </div>

              {/* ebaCredit Line Option */}
              <div className="border border-gray-200 rounded-lg p-4">
                <label className="flex items-start gap-3 cursor-pointer">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="ebacredit"
                    checked={selectedPaymentMethod === "ebacredit"}
                    onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                    className="mt-1 w-4 h-4 text-[#E63946] border-gray-300 focus:ring-[#E63946]"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="w-3 h-3 bg-[#0F2C59] rounded-full"></span>
                      <span className="font-medium text-gray-900">
                        Pay from ebaCredit line
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">
                      Available credit: EBA$ 5,000.00
                    </p>
                  </div>
                </label>
              </div>
            </div>

            {/* Terms and Continue */}
            <div className="px-6 pb-6">
              <p className="text-xs text-gray-500 mb-4">
                By proceeding, you agree to the{" "}
                <span className="text-[#E63946] underline cursor-pointer">
                  Payment Processing policy
                </span>
                ,{" "}
                <span className="text-[#E63946] underline cursor-pointer">
                  Terms of Service
                </span>{" "}
                and
              </p>

              <button
                onClick={() => {
                  setShowPaymentModal(false);
                  setShowConfirmModal(true);
                }}
                className="w-full bg-[#0F2C59] text-white py-3 px-4 rounded-lg font-medium hover:bg-[#0F2C59]/90"
              >
                Continue
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Confirm Purchase Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-lg mx-4 relative max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <button
                onClick={() => setShowConfirmModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <h2 className="text-lg font-semibold text-gray-900">
                Confirm Your Purchase
              </h2>
              <button
                onClick={() => setShowConfirmModal(false)}
                className="text-gray-400 hover:text-gray-600 text-xl font-bold"
              >
                ×
              </button>
            </div>

            {/* Purchase Summary */}
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-base font-semibold text-gray-900 mb-4">
                Purchase Summary
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Item</span>
                  <span className="text-sm font-medium text-gray-900">
                    {listing?.name || "Premium Gaming Laptop"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Price</span>
                  <span className="text-sm font-medium text-gray-900">
                    eBa$ {listing?.price || "1,299.99"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Seller</span>
                  <span className="text-sm font-medium text-[#0F2C59]">
                    {listing?.seller_info?.name || "TechTreasures"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Category</span>
                  <span className="text-sm font-medium text-[#0F2C59]">
                    {listing?.category || "Electronics > Gaming Laptops"}
                  </span>
                </div>
              </div>
            </div>

            {/* Delivery Options */}
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-base font-semibold text-gray-900 mb-4">
                Delivery Options
              </h3>
              <div className="space-y-3">
                {/* eba Delivery */}
                <div className="border border-[#E63946] bg-red-50 rounded-lg p-4">
                  <label className="flex items-start gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="deliveryMethod"
                      value="eba_delivery"
                      checked={selectedDeliveryMethod === "eba_delivery"}
                      onChange={(e) =>
                        setSelectedDeliveryMethod(e.target.value)
                      }
                      className="mt-1 w-4 h-4 text-[#E63946] border-gray-300 focus:ring-[#E63946]"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <svg
                          className="w-4 h-4 text-gray-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                          <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z" />
                        </svg>
                        <span className="font-medium text-gray-900">
                          eba Delivery
                        </span>
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                          Most Trusted
                        </span>
                        <svg
                          className="w-4 h-4 text-blue-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <p className="text-xs text-gray-600 mb-1">
                        4.7/5 (134 reviews) "Fast and secure delivery"
                      </p>
                      <p className="text-xs text-gray-500">
                        Trackable, platform-managed shipping
                      </p>
                    </div>
                  </label>
                </div>

                {/* Local Delivery */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <label className="flex items-start gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="deliveryMethod"
                      value="local_delivery"
                      checked={selectedDeliveryMethod === "local_delivery"}
                      onChange={(e) =>
                        setSelectedDeliveryMethod(e.target.value)
                      }
                      className="mt-1 w-4 h-4 text-[#E63946] border-gray-300 focus:ring-[#E63946]"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <svg
                          className="w-4 h-4 text-gray-400"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="font-medium text-gray-900">
                          Local Delivery
                        </span>
                        <svg
                          className="w-4 h-4 text-gray-400"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="flex items-center gap-1 mb-1">
                        <div className="flex text-yellow-400">
                          <span>★★★★★</span>
                        </div>
                        <span className="text-xs text-gray-600">
                          4.2/5 (45 reviews) "Easy drop-off, very convenient"
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        No need to be home
                      </p>
                    </div>
                  </label>
                </div>

                {/* International Delivery */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <label className="flex items-start gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="deliveryMethod"
                      value="international_delivery"
                      checked={
                        selectedDeliveryMethod === "international_delivery"
                      }
                      onChange={(e) =>
                        setSelectedDeliveryMethod(e.target.value)
                      }
                      className="mt-1 w-4 h-4 text-[#E63946] border-gray-300 focus:ring-[#E63946]"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <svg
                          className="w-4 h-4 text-gray-400"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="font-medium text-gray-900">
                          International Delivery
                        </span>
                      </div>
                      <div className="flex items-center gap-1 mb-1">
                        <div className="flex text-yellow-400">
                          <span>★★★★☆</span>
                        </div>
                        <span className="text-xs text-gray-600">
                          4.0/5 (27 reviews) "Arrived in excellent, but took a"
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        Not available in this area
                      </p>
                    </div>
                  </label>
                </div>

                {/* Pickup */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <label className="flex items-start gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="deliveryMethod"
                      value="pickup"
                      checked={selectedDeliveryMethod === "pickup"}
                      onChange={(e) =>
                        setSelectedDeliveryMethod(e.target.value)
                      }
                      className="mt-1 w-4 h-4 text-[#E63946] border-gray-300 focus:ring-[#E63946]"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <svg
                          className="w-4 h-4 text-gray-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM10 12a2 2 0 100-4 2 2 0 000 4z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="font-medium text-gray-900">
                          Pickup
                        </span>
                        <svg
                          className="w-4 h-4 text-blue-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="flex items-center gap-1 mb-1">
                        <div className="flex text-yellow-400">
                          <span>★★★★★</span>
                        </div>
                        <span className="text-xs text-gray-600">
                          4.5/5 (67 reviews) "Easy and quick hand-off"
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        Collect from seller's location
                      </p>
                    </div>
                  </label>
                </div>

                {/* Meet-up */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <label className="flex items-start gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="deliveryMethod"
                      value="meetup"
                      checked={selectedDeliveryMethod === "meetup"}
                      onChange={(e) =>
                        setSelectedDeliveryMethod(e.target.value)
                      }
                      className="mt-1 w-4 h-4 text-[#E63946] border-gray-300 focus:ring-[#E63946]"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <svg
                          className="w-4 h-4 text-gray-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                        </svg>
                        <span className="font-medium text-gray-900">
                          Meet-up
                        </span>
                        <svg
                          className="w-4 h-4 text-blue-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="flex items-center gap-1 mb-1">
                        <div className="flex text-yellow-400">
                          <span>★★★★☆</span>
                        </div>
                        <span className="text-xs text-gray-600">
                          4.2/5 (89 reviews) "Friendly and on-time"
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        Meet at a public location
                      </p>
                    </div>
                  </label>
                </div>
              </div>

              {/* Pickup/Delivery Info */}
              <div className="mt-4 p-3 bg-blue-50 rounded-lg flex items-start gap-2">
                <svg
                  className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
                <p className="text-xs text-blue-800">
                  After confirming purchase, the seller will contact you to
                  coordinate the pickup time and location.
                </p>
              </div>
            </div>

            {/* Shipping Address Form */}
            {(selectedDeliveryMethod === "eba_delivery" ||
              selectedDeliveryMethod === "local_delivery") && (
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-base font-semibold text-gray-900 mb-4">
                  Where should we ship this item?
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={shippingAddress.fullName}
                      onChange={(e) =>
                        setShippingAddress({
                          ...shippingAddress,
                          fullName: e.target.value,
                        })
                      }
                      placeholder="Alex Johnson"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={shippingAddress.phoneNumber}
                        onChange={(e) =>
                          setShippingAddress({
                            ...shippingAddress,
                            phoneNumber: e.target.value,
                          })
                        }
                        placeholder="(*************"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Country
                      </label>
                      <input
                        type="text"
                        value={shippingAddress.country}
                        onChange={(e) =>
                          setShippingAddress({
                            ...shippingAddress,
                            country: e.target.value,
                          })
                        }
                        placeholder="Country"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Address Line 1
                    </label>
                    <input
                      type="text"
                      value={shippingAddress.addressLine1}
                      onChange={(e) =>
                        setShippingAddress({
                          ...shippingAddress,
                          addressLine1: e.target.value,
                        })
                      }
                      placeholder="Street address"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Address Line 2 (Optional)
                    </label>
                    <input
                      type="text"
                      value={shippingAddress.addressLine2}
                      onChange={(e) =>
                        setShippingAddress({
                          ...shippingAddress,
                          addressLine2: e.target.value,
                        })
                      }
                      placeholder="Apartment, suite, unit, etc."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        City
                      </label>
                      <input
                        type="text"
                        value={shippingAddress.city}
                        onChange={(e) =>
                          setShippingAddress({
                            ...shippingAddress,
                            city: e.target.value,
                          })
                        }
                        placeholder="City"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Province/State
                      </label>
                      <input
                        type="text"
                        value={shippingAddress.provinceState}
                        onChange={(e) =>
                          setShippingAddress({
                            ...shippingAddress,
                            provinceState: e.target.value,
                          })
                        }
                        placeholder="Province/State"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Postal/ZIP Code
                    </label>
                    <input
                      type="text"
                      value={shippingAddress.postalCode}
                      onChange={(e) =>
                        setShippingAddress({
                          ...shippingAddress,
                          postalCode: e.target.value,
                        })
                      }
                      placeholder="Postal/ZIP Code"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                    />
                  </div>

                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="saveAddress"
                      checked={shippingAddress.saveToAccount}
                      onChange={(e) =>
                        setShippingAddress({
                          ...shippingAddress,
                          saveToAccount: e.target.checked,
                        })
                      }
                      className="w-4 h-4 text-[#0F2C59] border-gray-300 rounded focus:ring-[#0F2C59]"
                    />
                    <label
                      htmlFor="saveAddress"
                      className="text-sm text-gray-700"
                    >
                      Save this address to my account
                    </label>
                  </div>

                  <div className="p-3 bg-blue-50 rounded-lg flex items-start gap-2">
                    <svg
                      className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <p className="text-xs text-blue-800">
                      The seller only ships to addresses in Canada. Please
                      ensure your shipping address is within this region.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Order Total */}
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-base font-semibold text-gray-900 mb-4">
                Order Total
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Item Price</span>
                  <span className="font-medium">
                    eBa$ {listing?.price || "1,299.99"}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium">eBa$ 0.00</span>
                </div>
                <div className="border-t border-gray-200 pt-2">
                  <div className="flex justify-between text-base font-semibold">
                    <span>Total</span>
                    <span>eBa$ {listing?.price || "1,299.99"}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Confirm Purchase Button */}
            <div className="p-6">
              <button
                onClick={handlePurchase}
                disabled={purchasing}
                className="w-full bg-[#0F2C59] text-white py-3 px-4 rounded-lg font-medium hover:bg-[#0F2C59]/90 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {purchasing ? "Processing..." : "Confirm Purchase"}
              </button>
            </div>
          </div>
        </div>
      )}
    </MemberWrapper>
  );
};

export default MemberMarketplaceDetailPage;
