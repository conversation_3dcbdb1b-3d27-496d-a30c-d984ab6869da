import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { MkdInputV2 } from "@/components/MkdInputV2";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { useSDK } from "../../hooks/useSDK";
import { useToast } from "../../hooks/useToast";

interface DeliveryPartnerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: () => void;
  userProfile?: {
    email?: string;
    first_name?: string;
    last_name?: string;
    phone?: string;
    address?: string;
  };
}

interface IDeliveryPartnerForm {
  full_name: string;
  email: string;
  phone_number: string;
  location: string;
  reference1_name: string;
  reference1_relationship: string;
  reference1_phone: string;
  reference1_email: string;
  reference2_name: string;
  reference2_relationship: string;
  reference2_phone: string;
  reference2_email: string;
  emergency_name: string;
  emergency_relationship: string;
  emergency_phone: string;
  vehicle_type: string;
  vehicle_make_model: string;
  license_plate: string;
  availability: string;
  documents_valid?: boolean;
  terms_accepted?: boolean;
}

const DeliveryPartnerModal: React.FC<DeliveryPartnerModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  userProfile,
}) => {
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();

  const [currentStep, setCurrentStep] = useState(1);
  const [submitting, setSubmitting] = useState(false);
  const [applicationStatus, setApplicationStatus] = useState<{
    status: string;
    canReapply: boolean;
    rejectionReason?: string;
    application?: any;
  } | null>(null);
  const [checkingStatus, setCheckingStatus] = useState(false);
  const [vehiclePhotos, setVehiclePhotos] = useState<{
    [key: string]: any;
    front: null;
    rear: null;
    left: null;
    right: null;
  }>({
    front: null,
    rear: null,
    left: null,
    right: null,
  });
  const [documents, setDocuments] = useState<{
    [key: string]: any;
    license: null;
    insurance: null;
    government_id: null;
  }>({
    license: null,
    insurance: null,
    government_id: null,
  });
  const [uploadingFiles, setUploadingFiles] = useState<{
    [key: string]: boolean;
  }>({});

  const schema = yup.object({
    full_name: yup.string().required("Full name is required"),
    email: yup.string().email("Invalid email").required("Email is required"),
    phone_number: yup.string().required("Phone number is required"),
    location: yup.string().required("Location is required"),
    reference1_name: yup.string().required("Reference 1 name is required"),
    reference1_relationship: yup.string().required("Relationship is required"),
    reference1_phone: yup.string().required("Phone number is required"),
    reference1_email: yup
      .string()
      .email("Invalid email")
      .required("Email is required"),
    reference2_name: yup.string().required("Reference 2 name is required"),
    reference2_relationship: yup.string().required("Relationship is required"),
    reference2_phone: yup.string().required("Phone number is required"),
    reference2_email: yup
      .string()
      .email("Invalid email")
      .required("Email is required"),
    emergency_name: yup.string().required("Emergency contact name is required"),
    emergency_relationship: yup.string().required("Relationship is required"),
    emergency_phone: yup.string().required("Phone number is required"),
    vehicle_type: yup.string().required("Vehicle type is required"),
    vehicle_make_model: yup
      .string()
      .required("Vehicle make and model is required"),
    license_plate: yup.string().required("License plate is required"),
    availability: yup.string().required("Availability is required"),
    documents_valid: yup
      .boolean()
      .oneOf([true], "You must confirm documents are valid"),
    terms_accepted: yup
      .boolean()
      .oneOf([true], "You must accept terms and conditions"),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      full_name: userProfile
        ? `${userProfile.first_name || ""} ${userProfile.last_name || ""}`.trim()
        : "",
      email: userProfile?.email || "",
      phone_number: userProfile?.phone || "",
      location: userProfile?.address || "",
    },
  });

  // Check application status when modal opens
  useEffect(() => {
    if (isOpen) {
      checkApplicationStatus();
    }
  }, [isOpen]);

  const checkApplicationStatus = async () => {
    try {
      setCheckingStatus(true);
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/delivery-agent/status",
        method: "GET",
      });

      if (!response.error && response.data) {
        setApplicationStatus(response.data);
      } else {
        setApplicationStatus({
          status: "not_registered",
          canReapply: true,
        });
      }
    } catch (error) {
      console.error("Error checking application status:", error);
      setApplicationStatus({
        status: "not_registered",
        canReapply: true,
      });
    } finally {
      setCheckingStatus(false);
    }
  };

  const handleFormSubmit = async (data: IDeliveryPartnerForm) => {
    try {
      setSubmitting(true);
      console.log("Form submitted:", data);

      // Submit delivery agent application
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/delivery-agent/apply",
        method: "POST",
        body: {
          fullName: data.full_name,
          email: data.email,
          phoneNumber: data.phone_number,
          location: data.location,
          reference1: {
            name: data.reference1_name,
            relationship: data.reference1_relationship,
            phone: data.reference1_phone,
            email: data.reference1_email,
          },
          reference2: {
            name: data.reference2_name,
            relationship: data.reference2_relationship,
            phone: data.reference2_phone,
            email: data.reference2_email,
          },
          emergencyContact: {
            name: data.emergency_name,
            relationship: data.emergency_relationship,
            phone: data.emergency_phone,
          },
          vehicleInfo: {
            type: data.vehicle_type,
            makeModel: data.vehicle_make_model,
            licensePlate: data.license_plate,
          },
          availability: data.availability,
          documentsValid: data.documents_valid,
          termsAccepted: data.terms_accepted,
          vehiclePhotos,
          documents,
        },
      });

      console.log("Delivery Agent Application API Response:", response);

      if (!response.error) {
        success(
          applicationStatus?.status === "rejected"
            ? "Application resubmitted successfully! We'll review your updated application within 2-3 business days."
            : "Application submitted successfully! We'll review your application within 2-3 business days."
        );

        // Refresh application status
        await checkApplicationStatus();

        if (onSubmit) {
          onSubmit();
        }
        onClose();
      } else {
        showError(String(response.message || "Failed to submit application"));
      }
    } catch (error: any) {
      console.error("Error submitting application:", error);
      showError(String(error?.message || "Failed to submit application"));
    } finally {
      setSubmitting(false);
    }
  };

  // Prefill function with test data
  const handlePrefillForm = () => {
    const testData = {
      full_name: "John Doe",
      email: "<EMAIL>",
      phone_number: "+****************",
      location: "123 Main Street, New York, NY 10001",
      reference1_name: "Sarah Johnson",
      reference1_relationship: "Former Supervisor",
      reference1_phone: "+****************",
      reference1_email: "<EMAIL>",
      reference2_name: "Mike Wilson",
      reference2_relationship: "Colleague",
      reference2_phone: "+****************",
      reference2_email: "<EMAIL>",
      emergency_name: "Jane Doe",
      emergency_relationship: "Spouse",
      emergency_phone: "+****************",
      vehicle_type: "Car",
      vehicle_make_model: "Toyota Camry 2020",
      license_plate: "ABC-1234",
      availability: "Weekdays 9AM-5PM, Weekends flexible",
      documents_valid: true,
      terms_accepted: true,
    };

    // Set all form values
    Object.entries(testData).forEach(([key, value]) => {
      setValue(key as keyof IDeliveryPartnerForm, value);
    });

    // Set test vehicle photos for preview demonstration
    setVehiclePhotos({
      front:
        "https://images.unsplash.com/photo-**********-f129b911e442?w=300&h=200&fit=crop",
      rear: "https://images.unsplash.com/photo-**********-da3b142c6e3d?w=300&h=200&fit=crop",
      left: "https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=300&h=200&fit=crop",
      right:
        "https://images.unsplash.com/photo-1580273916550-e323be2ae537?w=300&h=200&fit=crop",
    });

    // Set test documents for demonstration
    setDocuments({
      license:
        "https://images.unsplash.com/photo-**********-6726b3ff858f?w=300&h=200&fit=crop",
      insurance:
        "https://images.unsplash.com/photo-**********-26032fced8bd?w=300&h=200&fit=crop",
      government_id:
        "https://images.unsplash.com/photo-**********-22dec7ec8818?w=300&h=200&fit=crop",
    });

    success("Form prefilled with test data and sample images!");
  };

  const handleFileUpload = async (
    file: File,
    type: string,
    category: string
  ) => {
    try {
      setUploadingFiles((prev) => ({ ...prev, [`${category}_${type}`]: true }));

      // Upload file to get URL
      const uploadResponse = await sdk.upload(file);

      if (uploadResponse.error) {
        showError(String(uploadResponse.message || "Failed to upload file"));
        return;
      }

      const fileUrl = uploadResponse.data?.url || uploadResponse.url;

      // Update state based on category
      if (category === "vehicle") {
        setVehiclePhotos((prev) => ({ ...prev, [type]: fileUrl }));
        success(`Vehicle ${type} photo uploaded successfully!`);
      } else if (category === "document") {
        setDocuments((prev) => ({ ...prev, [type]: fileUrl }));
        success(`${type} document uploaded successfully!`);
      }
    } catch (error: any) {
      console.error(`Error uploading ${category} ${type}:`, error);
      showError(
        String(error?.message || `Failed to upload ${category} ${type}`)
      );
    } finally {
      setUploadingFiles((prev) => ({
        ...prev,
        [`${category}_${type}`]: false,
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-[#0F2C59]">
            Become an eBa Delivery Partner
          </h2>
          <div className="flex items-center gap-3">
            <InteractiveButton
              type="button"
              onClick={handlePrefillForm}
              className="bg-yellow-500 hover:bg-yellow-600 !text-white px-3 py-1 rounded text-sm font-medium"
            >
              Prefill Test Data
            </InteractiveButton>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-xl font-bold"
            >
              ×
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6">
          {/* Description */}
          <p className="text-gray-600 mb-6">
            Help users receive their purchases safely while earning with
            flexible delivery jobs.
          </p>

          {/* Application Status Display */}
          {checkingStatus && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
                <span className="text-blue-700">
                  Checking application status...
                </span>
              </div>
            </div>
          )}

          {applicationStatus &&
            applicationStatus.status !== "not_registered" && (
              <div className="mb-6">
                {applicationStatus.status === "pending" && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white text-xs">⏳</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-yellow-800">
                          Application Under Review
                        </h4>
                        <p className="text-yellow-700 text-sm">
                          Your delivery agent application is currently being
                          reviewed. We'll notify you within 2-3 business days.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {applicationStatus.status === "active" && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-green-800">
                          Application Approved
                        </h4>
                        <p className="text-green-700 text-sm">
                          Congratulations! Your delivery agent application has
                          been approved. You can now start accepting delivery
                          requests.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {applicationStatus.status === "rejected" && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white text-xs">✗</span>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-red-800">
                          Application Rejected
                        </h4>
                        <p className="text-red-700 text-sm mb-2">
                          Unfortunately, your previous application was not
                          approved.
                        </p>
                        {applicationStatus.rejectionReason && (
                          <div className="bg-red-100 p-3 rounded border border-red-200">
                            <p className="text-red-800 text-sm font-medium">
                              Reason:
                            </p>
                            <p className="text-red-700 text-sm">
                              {applicationStatus.rejectionReason}
                            </p>
                          </div>
                        )}
                        {applicationStatus.canReapply && (
                          <p className="text-red-700 text-sm mt-2">
                            <strong>Good news:</strong> You can submit a new
                            application below. Please address the feedback
                            provided above.
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

          {/* Hide form if application is pending or approved */}
          {(!applicationStatus ||
            applicationStatus.canReapply ||
            applicationStatus.status === "not_registered") && (
            <>
              {/* Your Information Section */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Your Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Full Name */}
                  <div>
                    <MkdInputV2
                      register={register}
                      name="full_name"
                      type="text"
                      errors={errors.full_name?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>Full Name*</MkdInputV2.Label>
                        <MkdInputV2.Field
                          placeholder="Alex Johnson"
                          className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                        />
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                  </div>

                  {/* Email */}
                  <div>
                    <MkdInputV2
                      register={register}
                      name="email"
                      type="email"
                      errors={errors.email?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>Email*</MkdInputV2.Label>
                        <MkdInputV2.Field
                          placeholder="<EMAIL>"
                          className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                        />
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                  </div>

                  {/* Phone Number */}
                  <div>
                    <MkdInputV2
                      register={register}
                      name="phone_number"
                      type="text"
                      errors={errors.phone_number?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>Phone Number*</MkdInputV2.Label>
                        <MkdInputV2.Field
                          placeholder="+****************"
                          className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                        />
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                  </div>

                  {/* Location */}
                  <div>
                    <MkdInputV2
                      register={register}
                      name="location"
                      type="text"
                      errors={errors.location?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>Location*</MkdInputV2.Label>
                        <MkdInputV2.Field
                          placeholder="New York, USA"
                          className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                        />
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                  </div>
                </div>
              </div>

              {/* References Section */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  References *
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Reference 1 */}
                  <div>
                    <h4 className="text-base font-medium text-gray-700 mb-3">
                      Reference 1
                    </h4>

                    <div className="space-y-3">
                      <MkdInputV2
                        register={register}
                        name="reference1_name"
                        type="text"
                        errors={errors.reference1_name?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Full Name*</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="Reference full name"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>

                      <MkdInputV2
                        register={register}
                        name="reference1_relationship"
                        type="text"
                        errors={errors.reference1_relationship?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Relationship*</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="e.g. Supervisor, Colleague"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>

                      <MkdInputV2
                        register={register}
                        name="reference1_phone"
                        type="text"
                        errors={errors.reference1_phone?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Phone Number*</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="e.g. +****************"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>

                      <MkdInputV2
                        register={register}
                        name="reference1_email"
                        type="email"
                        errors={errors.reference1_email?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Email*</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="<EMAIL>"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </div>
                  </div>

                  {/* Reference 2 */}
                  <div>
                    <h4 className="text-base font-medium text-gray-700 mb-3">
                      Reference 2
                    </h4>

                    <div className="space-y-3">
                      <MkdInputV2
                        register={register}
                        name="reference2_name"
                        type="text"
                        errors={errors.reference2_name?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Full Name*</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="Reference full name"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>

                      <MkdInputV2
                        register={register}
                        name="reference2_relationship"
                        type="text"
                        errors={errors.reference2_relationship?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Relationship*</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="e.g. Supervisor, Colleague"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>

                      <MkdInputV2
                        register={register}
                        name="reference2_phone"
                        type="text"
                        errors={errors.reference2_phone?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Phone Number*</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="e.g. +****************"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>

                      <MkdInputV2
                        register={register}
                        name="reference2_email"
                        type="email"
                        errors={errors.reference2_email?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Email*</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="<EMAIL>"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </div>
                  </div>
                </div>
              </div>

              {/* Emergency Contact Section */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Emergency Contact *
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <MkdInputV2
                    register={register}
                    name="emergency_name"
                    type="text"
                    errors={errors.emergency_name?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Full Name*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Emergency contact name"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>

                  <MkdInputV2
                    register={register}
                    name="emergency_relationship"
                    type="text"
                    errors={errors.emergency_relationship?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Relationship*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="e.g. Spouse, Parent"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>

                  <MkdInputV2
                    register={register}
                    name="emergency_phone"
                    type="text"
                    errors={errors.emergency_phone?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Phone Number*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="e.g. +****************"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>
              </div>

              {/* Vehicle & Legal Details Section */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Vehicle & Legal Details
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <MkdInputV2
                      register={register}
                      name="vehicle_type"
                      type="text"
                      errors={errors.vehicle_type?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>Vehicle Type*</MkdInputV2.Label>
                        <select
                          {...register("vehicle_type")}
                          className="w-full px-3 py-2 border border-[#D1D5DB] rounded-md bg-white text-black focus:outline-none focus:ring-2 focus:ring-[#0F2C59]"
                        >
                          <option value="">Select vehicle type</option>
                          <option value="car">Car</option>
                          <option value="motorcycle">Motorcycle</option>
                          <option value="bicycle">Bicycle</option>
                          <option value="van">Van</option>
                        </select>
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                  </div>

                  <MkdInputV2
                    register={register}
                    name="vehicle_make_model"
                    type="text"
                    errors={errors.vehicle_make_model?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Vehicle Make & Model*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="e.g. Toyota Prius 2022"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <MkdInputV2
                    register={register}
                    name="license_plate"
                    type="text"
                    errors={errors.license_plate?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>License Plate Number*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="e.g. ABC-1234"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>

                  <div>
                    <MkdInputV2
                      register={register}
                      name="availability"
                      type="text"
                      errors={errors.availability?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>Availability*</MkdInputV2.Label>
                        <select
                          {...register("availability")}
                          className="w-full px-3 py-2 border border-[#D1D5DB] rounded-md bg-white text-black focus:outline-none focus:ring-2 focus:ring-[#0F2C59]"
                        >
                          <option value="">Select availability</option>
                          <option value="monday">Monday</option>
                          <option value="tuesday">Tuesday</option>
                          <option value="wednesday">Wednesday</option>
                          <option value="thursday">Thursday</option>
                          <option value="friday">Friday</option>
                          <option value="saturday">Saturday</option>
                          <option value="sunday">Sunday</option>
                          <option value="weekdays">Weekdays</option>
                          <option value="weekends">Weekends</option>
                          <option value="full-time">Full Time</option>
                        </select>
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                    <p className="text-xs text-gray-500 mt-1">
                      Hold Ctrl/Cmd to select multiple days
                    </p>
                  </div>
                </div>
              </div>

              {/* Vehicle Photos Section */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Vehicle Photos *
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Please upload clear photos of your vehicle from all sides for
                  verification purposes
                </p>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {[
                    { key: "front", label: "Front View", icon: "🚗" },
                    { key: "rear", label: "Rear View", icon: "🚗" },
                    { key: "left", label: "Left Side", icon: "🚗" },
                    { key: "right", label: "Right Side", icon: "🚗" },
                  ].map((photo) => (
                    <div key={photo.key} className="text-center">
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-2 mb-2 hover:border-[#0F2C59] cursor-pointer relative min-h-[120px] flex flex-col items-center justify-center">
                        {vehiclePhotos[photo.key] ? (
                          // Show image preview when photo is uploaded
                          <div className="w-full h-full relative">
                            <img
                              src={vehiclePhotos[photo.key]}
                              alt={photo.label}
                              className="w-full h-full object-cover rounded-md max-h-[100px]"
                            />
                            <div className="absolute top-1 right-1">
                              <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <span className="text-white text-xs">✓</span>
                              </div>
                            </div>
                            <div className="absolute bottom-1 left-1 right-1">
                              <p className="text-xs font-medium text-white bg-black bg-opacity-60 px-1 py-0.5 rounded text-center">
                                {photo.label}
                              </p>
                            </div>
                          </div>
                        ) : (
                          // Show placeholder when no photo is uploaded
                          <>
                            <div className="text-2xl mb-2">{photo.icon}</div>
                            <p className="text-sm font-medium text-gray-700 mb-1">
                              {photo.label}
                            </p>
                            <p className="text-xs text-gray-500">
                              Click to upload
                            </p>
                          </>
                        )}
                        {uploadingFiles[`vehicle_${photo.key}`] && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                            <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          </div>
                        )}
                      </div>
                      <label className="text-sm text-[#0F2C59] hover:underline cursor-pointer">
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              handleFileUpload(file, photo.key, "vehicle");
                            }
                          }}
                          disabled={uploadingFiles[`vehicle_${photo.key}`]}
                        />
                        {uploadingFiles[`vehicle_${photo.key}`]
                          ? "Uploading..."
                          : vehiclePhotos[photo.key]
                            ? "Replace"
                            : "Upload"}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Required Documents Section */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Required Documents
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[
                    { key: "license", label: "Driver's License", icon: "🆔" },
                    {
                      key: "insurance",
                      label: "Vehicle Insurance",
                      icon: "📄",
                    },
                    {
                      key: "government_id",
                      label: "Government-issued ID",
                      icon: "📋",
                    },
                  ].map((doc) => (
                    <div key={doc.key} className="text-center">
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-2 mb-2 hover:border-[#0F2C59] cursor-pointer relative min-h-[140px] flex flex-col items-center justify-center">
                        {documents[doc.key] ? (
                          // Show document preview when uploaded
                          <div className="w-full h-full relative">
                            {documents[doc.key]
                              .toLowerCase()
                              .includes(".pdf") ? (
                              // PDF preview
                              <div className="w-full h-full flex flex-col items-center justify-center bg-red-50 rounded-md min-h-[120px]">
                                <div className="text-3xl mb-2">📄</div>
                                <p className="text-xs font-medium text-gray-700 text-center">
                                  PDF Document
                                </p>
                              </div>
                            ) : (
                              // Image preview
                              <img
                                src={documents[doc.key]}
                                alt={doc.label}
                                className="w-full h-full object-cover rounded-md max-h-[120px]"
                              />
                            )}
                            <div className="absolute top-1 right-1">
                              <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <span className="text-white text-xs">✓</span>
                              </div>
                            </div>
                            <div className="absolute bottom-1 left-1 right-1">
                              <p className="text-xs font-medium text-white bg-black bg-opacity-60 px-1 py-0.5 rounded text-center">
                                {doc.label}
                              </p>
                            </div>
                          </div>
                        ) : (
                          // Show placeholder when no document is uploaded
                          <>
                            <div className="text-2xl mb-2">{doc.icon}</div>
                            <p className="text-sm font-medium text-gray-700 mb-1">
                              {doc.label} *
                            </p>
                            <p className="text-xs text-gray-500 text-center">
                              Click to upload or drag and drop
                              <br />
                              PDF, JPG or PNG (max 5MB)
                            </p>
                          </>
                        )}
                        {uploadingFiles[`document_${doc.key}`] && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                            <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          </div>
                        )}
                      </div>
                      <label className="text-sm text-[#0F2C59] hover:underline cursor-pointer">
                        <input
                          type="file"
                          accept=".pdf,.jpg,.jpeg,.png"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              handleFileUpload(file, doc.key, "document");
                            }
                          }}
                          disabled={uploadingFiles[`document_${doc.key}`]}
                        />
                        {uploadingFiles[`document_${doc.key}`]
                          ? "Uploading..."
                          : documents[doc.key]
                            ? "Replace File"
                            : "Browse Files"}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Checkboxes */}
              <div className="mb-6 space-y-3">
                <label className="flex items-start">
                  <input
                    type="checkbox"
                    {...register("documents_valid")}
                    className="mt-1 mr-3 h-4 w-4 text-[#0F2C59] focus:ring-[#0F2C59] border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">
                    I confirm that my documents are valid and up to date
                  </span>
                </label>

                <label className="flex items-start">
                  <input
                    type="checkbox"
                    {...register("terms_accepted")}
                    className="mt-1 mr-3 h-4 w-4 text-[#0F2C59] focus:ring-[#0F2C59] border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">
                    I agree to the{" "}
                    <button
                      type="button"
                      className="text-[#0F2C59] hover:underline"
                    >
                      Delivery Terms and Conditions
                    </button>
                  </span>
                </label>
              </div>

              {/* Info Box */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-[#0F2C59] rounded-full flex items-center justify-center mr-3 mt-0.5">
                    <span className="text-white text-xs">ℹ</span>
                  </div>
                  <div className="text-sm text-gray-700">
                    <strong>
                      After submitting your application, our team will review
                      your details.
                    </strong>{" "}
                    This process typically takes 2-3 business days. You'll
                    receive an email notification once your application has been
                    reviewed.
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3">
                <InteractiveButton
                  type="button"
                  onClick={onClose}
                  className="px-6 py-2 border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 rounded-md"
                >
                  Cancel
                </InteractiveButton>
                <InteractiveButton
                  type="submit"
                  loading={submitting}
                  disabled={submitting}
                  className="px-6 py-2 bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white rounded-md"
                >
                  {applicationStatus?.status === "rejected"
                    ? "Resubmit Application"
                    : "Submit Application"}
                </InteractiveButton>
              </div>
            </>
          )}

          {/* Show close button for non-reapplicable statuses */}
          {applicationStatus &&
            !applicationStatus.canReapply &&
            applicationStatus.status !== "not_registered" && (
              <div className="flex justify-end">
                <InteractiveButton
                  type="button"
                  onClick={onClose}
                  className="px-6 py-2 bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white rounded-md"
                >
                  Close
                </InteractiveButton>
              </div>
            )}
        </form>
      </div>
    </div>
  );
};

export default DeliveryPartnerModal;
